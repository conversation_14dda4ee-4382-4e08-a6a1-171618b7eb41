# 更新日志

## 新增功能 - Token 命令

### 新增 Token 命令
- **命令**: `token`
- **用途**: 单独获取和显示访问令牌
- **特点**: 不需要输入文件，只需要主机连接参数
- **输出**: 显示获取的 token 并保存到 .token 文件

### 使用示例
```bash
# 获取 token
./jl_client_bin.exe -action token -host *************

# 使用自定义端口和协议获取 token
./jl_client_bin.exe -action token -host ************* -port 8888 -protocol https
```

### 功能说明
1. 自动获取随机数（如果需要）
2. 计算 SM3 加密密钥
3. 获取访问令牌
4. 显示令牌内容
5. 保存令牌到 .token 文件

---

## 新增功能 - 命令行参数扩展

### 新增的命令行参数

1. **接口 IP/主机名** (`-host`)
   - 参数：`-host <ip_address_or_hostname>`
   - 必需参数
   - 示例：`-host *************` 或 `-host api.example.com`

2. **接口端口** (`-port`)
   - 参数：`-port <port_number>`
   - 可选参数，默认值：`8443`
   - 示例：`-port 8080` 或 `-port 9443`

3. **接口协议** (`-protocol`)
   - 参数：`-protocol <http|https>`
   - 可选参数，默认值：`https`
   - 支持的值：`http` 或 `https`
   - 示例：`-protocol http` 或 `-protocol https`

### 更新后的命令格式

```bash
./jl_client_bin.exe -i <input_file> -action <action> -host <host> [-port <port>] [-protocol <protocol>]
```

### 使用示例

#### 基本使用（使用默认端口和协议）
```bash
./jl_client_bin.exe -i examples/register.json -action register -host *************
```

#### 使用自定义端口
```bash
./jl_client_bin.exe -i examples/status.json -action status -host ************* -port 8080
```

#### 使用 HTTP 协议
```bash
./jl_client_bin.exe -i examples/upload.json -action upload -host ************* -protocol http -port 8080
```

#### 使用域名
```bash
./jl_client_bin.exe -i examples/download.json -action download -host api.example.com -port 9443 -protocol https
```

### 代码变更

1. **常量定义更新**
   ```go
   const (
       DefaultProtocol = "https"
       DefaultPort     = "8443"
       BasePath        = "/cnr/api/v2/asscis/api/routing"
   )
   ```

2. **NewClient 函数更新**
   - 新增参数：`protocol`, `host`, `port`
   - 动态构建 BaseURL：`fmt.Sprintf("%s://%s:%s%s", protocol, host, port, BasePath)`

3. **main 函数更新**
   - 新增命令行参数解析
   - 添加参数验证逻辑
   - 支持协议验证（只允许 http 或 https）

### 向后兼容性

- 保持所有原有功能不变
- 新增的参数都有合理的默认值
- 原有的 JSON 输入文件格式保持不变

### 错误处理

程序会在以下情况下报错并退出：
1. 未提供必需的 `-host` 参数
2. 提供了无效的协议（不是 http 或 https）
3. 输入文件不存在
4. 缺少必需的 `-i` 或 `-action` 参数

### 配置文件

`.env` 文件格式保持不变：
```
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
```

### 编译和运行

编译命令保持不变：
```bash
go build -o jl_client_bin.exe main.go
```

或使用 Makefile：
```bash
make build
```

### 测试

更新了测试脚本和 Makefile 示例：
- `test.bat` 包含新参数的测试用例
- Makefile 示例需要设置 HOST 变量

### 文档更新

- 更新了 README.md
- 更新了 USAGE.md
- 更新了 Makefile 帮助信息
- 新增了 CHANGELOG.md

### 注意事项

1. 所有示例命令都需要提供 `-host` 参数
2. 默认使用 HTTPS 协议和 8443 端口
3. 程序会忽略 SSL 证书验证（适用于测试环境）
4. 网络连接超时设置为 30 秒
