# JL Client 使用指南

## 项目概述

这是一个用 Go 语言编写的客户端工具，用于与 JG 接口系统进行交互。该工具实现了完整的接口调用功能，包括认证、注册、状态上报、数据上传和下载等功能。

## 主要特性

1. **自动认证管理**：自动处理随机数获取和 Token 认证流程
2. **SM3 加密**：使用国密 SM3 算法进行安全认证
3. **配置文件管理**：支持 .env 配置文件和 JSON 输入文件
4. **命令行接口**：提供简洁的命令行操作方式
5. **错误处理**：完善的错误处理和日志输出

## 文件结构

```
jl_client/
├── main.go                 # 主程序文件
├── go.mod                  # Go 模块文件
├── go.sum                  # 依赖校验文件
├── .env                    # 配置文件（需要用户配置）
├── Makefile               # 编译脚本
├── README.md              # 详细说明文档
├── USAGE.md               # 使用指南
├── test.bat               # Windows 测试脚本
├── examples/              # 示例输入文件目录
│   ├── register.json      # 注册示例
│   ├── update.json        # 更新示例
│   ├── status.json        # 状态上报示例
│   ├── upload.json        # 数据上传示例
│   └── download.json      # 数据下载示例
└── 运行时生成的文件：
    ├── .randomNum         # 存储随机数
    ├── .token             # 存储访问令牌
    └── jl_client_bin.exe  # 编译后的可执行文件
```

## 快速开始

### 1. 环境准备

确保已安装 Go 1.21 或更高版本：
```bash
go version
```

### 2. 下载依赖

```bash
go mod tidy
```

### 3. 配置环境

编辑 `.env` 文件，设置你的客户端凭据：
```
CLIENT_ID=your_actual_client_id
CLIENT_SECRET=your_actual_client_secret
```

### 4. 编译程序

```bash
go build -o jl_client_bin.exe main.go
```

或使用 Makefile：
```bash
make build
```

### 5. 运行程序

基本命令格式：
```bash
./jl_client_bin.exe -i <input_file> -action <action_name>
```

## 支持的操作

### 1. 系统注册
```bash
./jl_client_bin.exe -i examples/register.json -action register
```

### 2. 系统信息更新
```bash
./jl_client_bin.exe -i examples/update.json -action update
```

### 3. 系统状态上报
```bash
./jl_client_bin.exe -i examples/status.json -action status
```

### 4. 数据上报
```bash
./jl_client_bin.exe -i examples/upload.json -action upload
```

### 5. 数据下载
```bash
./jl_client_bin.exe -i examples/download.json -action download
```

## 输入文件格式说明

### 注册文件 (register.json)
```json
{
    "org": "单位名称",
    "org_type": "单位类别",
    "secret_qualification": "保密资质",
    "net_license_number": "涉密网络运行许可证书编号",
    "net_name": "涉密网络名称",
    "net_type": "网络类型",
    "zjg_license_number": "保密自监管软件系统证书编号",
    "USCI_number": "统一社会信用代码",
    "postal_code": "区域邮政编码",
    "usciNumber": "统一社会信用代码"
}
```

### 状态上报文件 (status.json)
```json
{
    "ssa_run_state": 1
}
```

### 数据上报文件 (upload.json)
```json
{
    "type": 1,
    "data": {
        "event_id": "事件ID",
        "event_name": "事件名称",
        "description": "事件描述"
    }
}
```

## 程序工作流程

1. **启动程序**：读取 .env 配置文件，获取客户端凭据
2. **认证流程**：
   - 检查是否存在有效的 Token
   - 如果没有，自动获取随机数
   - 使用 SM3 算法计算认证密钥
   - 获取访问 Token 并保存
3. **执行操作**：根据指定的 action 执行相应的接口调用
4. **结果输出**：显示操作结果和状态信息

## 安全特性

1. **SM3 加密**：使用国密 SM3 算法进行密钥计算
2. **Token 缓存**：Token 自动缓存，避免频繁认证
3. **HTTPS 支持**：支持 HTTPS 连接（测试环境忽略证书验证）
4. **配置隔离**：敏感信息存储在 .env 文件中

## 错误处理

程序包含完善的错误处理机制：
- 网络连接错误
- 认证失败
- 接口调用失败
- 文件读取错误
- JSON 解析错误

## 注意事项

1. 确保 `.env` 文件中的 CLIENT_ID 和 CLIENT_SECRET 正确
2. 确保网络连接正常，能够访问接口服务器
3. 输入的 JSON 文件格式必须正确
4. 程序会自动处理 Token 过期和重新认证
5. 在生产环境中，建议启用 SSL 证书验证

## 故障排除

### 常见问题

1. **编译失败**：检查 Go 版本和依赖是否正确安装
2. **认证失败**：检查 CLIENT_ID 和 CLIENT_SECRET 是否正确
3. **网络错误**：检查网络连接和防火墙设置
4. **文件不存在**：确保输入文件路径正确

### 调试方法

1. 检查 `.randomNum` 和 `.token` 文件是否正确生成
2. 查看程序输出的错误信息
3. 验证输入 JSON 文件格式
4. 测试网络连接到接口服务器

## 开发和扩展

如需修改或扩展功能：

1. 修改 `main.go` 中的相关函数
2. 更新接口 URL 或参数格式
3. 添加新的操作类型
4. 重新编译程序

## 技术支持

如遇到问题，请检查：
1. Go 环境是否正确安装
2. 依赖包是否正确下载
3. 配置文件是否正确设置
4. 网络连接是否正常
