@echo off
echo Testing jl_client compilation...
go build -o jl_client_bin.exe main.go
if %errorlevel% neq 0 (
    echo Build failed!
    exit /b 1
)

echo Build successful!
echo.
echo Testing program without arguments...
jl_client_bin.exe
echo.
echo Testing program with missing host...
jl_client_bin.exe -i examples/status.json -action status
echo.
echo Testing program with invalid protocol...
jl_client_bin.exe -i examples/status.json -action status -host ************* -protocol ftp
echo.
echo Testing program with valid parameters (will fail due to network)...
jl_client_bin.exe -i examples/status.json -action status -host ************* -port 8443 -protocol https
echo.
echo Testing program with HTTP protocol...
jl_client_bin.exe -i examples/status.json -action status -host ************* -port 8080 -protocol http
