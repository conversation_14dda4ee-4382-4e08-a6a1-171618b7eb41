# Makefile for jl_client

# 变量定义
BINARY_NAME=jl_client_bin
SOURCE_FILE=main.go
BUILD_DIR=build

# 默认目标
.PHONY: all
all: build

# 下载依赖
.PHONY: deps
deps:
	go mod tidy
	go mod download

# 编译
.PHONY: build
build: deps
	go build -o $(BINARY_NAME) $(SOURCE_FILE)

# 编译到指定目录
.PHONY: build-dir
build-dir: deps
	mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(SOURCE_FILE)

# 交叉编译 - Linux
.PHONY: build-linux
build-linux: deps
	GOOS=linux GOARCH=amd64 go build -o $(BINARY_NAME)-linux $(SOURCE_FILE)

# 交叉编译 - Windows
.PHONY: build-windows
build-windows: deps
	GOOS=windows GOARCH=amd64 go build -o $(BINARY_NAME).exe $(SOURCE_FILE)

# 清理
.PHONY: clean
clean:
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_NAME)-linux
	rm -f $(BINARY_NAME).exe
	rm -rf $(BUILD_DIR)
	rm -f .randomNum
	rm -f .token

# 测试编译
.PHONY: test-build
test-build:
	go build -o /dev/null $(SOURCE_FILE)

# 格式化代码
.PHONY: fmt
fmt:
	go fmt ./...

# 检查代码
.PHONY: vet
vet:
	go vet ./...

# 运行示例
.PHONY: example-register
example-register: build
	./$(BINARY_NAME) -i examples/register.json -action register

.PHONY: example-status
example-status: build
	./$(BINARY_NAME) -i examples/status.json -action status

.PHONY: example-upload
example-upload: build
	./$(BINARY_NAME) -i examples/upload.json -action upload

.PHONY: example-download
example-download: build
	./$(BINARY_NAME) -i examples/download.json -action download

# 帮助
.PHONY: help
help:
	@echo "可用的命令:"
	@echo "  make build          - 编译程序"
	@echo "  make build-dir      - 编译到 build 目录"
	@echo "  make build-linux    - 交叉编译 Linux 版本"
	@echo "  make build-windows  - 交叉编译 Windows 版本"
	@echo "  make deps           - 下载依赖"
	@echo "  make clean          - 清理编译文件"
	@echo "  make fmt            - 格式化代码"
	@echo "  make vet            - 检查代码"
	@echo "  make example-*      - 运行示例"
	@echo "  make help           - 显示帮助"
