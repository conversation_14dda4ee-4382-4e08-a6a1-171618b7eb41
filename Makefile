# Makefile for jl_client

# 变量定义
BINARY_NAME=jl_client_bin
SOURCE_FILE=main.go
BUILD_DIR=build

# 默认目标
.PHONY: all
all: build

# 下载依赖
.PHONY: deps
deps:
	go mod tidy
	go mod download

# 编译
.PHONY: build
build: deps
	go build -o $(BINARY_NAME) $(SOURCE_FILE)

# 编译到指定目录
.PHONY: build-dir
build-dir: deps
	mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(SOURCE_FILE)

# 交叉编译 - Linux
.PHONY: build-linux
build-linux: deps
	GOOS=linux GOARCH=amd64 go build -o $(BINARY_NAME)-linux $(SOURCE_FILE)

# 交叉编译 - Windows
.PHONY: build-windows
build-windows: deps
	GOOS=windows GOARCH=amd64 go build -o $(BINARY_NAME).exe $(SOURCE_FILE)

# 清理
.PHONY: clean
clean:
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_NAME)-linux
	rm -f $(BINARY_NAME).exe
	rm -rf $(BUILD_DIR)
	rm -f .randomNum
	rm -f .token

# 测试编译
.PHONY: test-build
test-build:
	go build -o /dev/null $(SOURCE_FILE)

# 格式化代码
.PHONY: fmt
fmt:
	go fmt ./...

# 检查代码
.PHONY: vet
vet:
	go vet ./...

# 运行示例 (需要设置 HOST 变量)
# 使用方法: make example-register HOST=*************
.PHONY: example-register
example-register: build
	@if [ -z "$(HOST)" ]; then echo "请设置 HOST 变量，例如: make example-register HOST=*************"; exit 1; fi
	./$(BINARY_NAME) -i examples/register.json -action register -host $(HOST)

.PHONY: example-status
example-status: build
	@if [ -z "$(HOST)" ]; then echo "请设置 HOST 变量，例如: make example-status HOST=*************"; exit 1; fi
	./$(BINARY_NAME) -i examples/status.json -action status -host $(HOST)

.PHONY: example-upload
example-upload: build
	@if [ -z "$(HOST)" ]; then echo "请设置 HOST 变量，例如: make example-upload HOST=*************"; exit 1; fi
	./$(BINARY_NAME) -i examples/upload.json -action upload -host $(HOST)

.PHONY: example-download
example-download: build
	@if [ -z "$(HOST)" ]; then echo "请设置 HOST 变量，例如: make example-download HOST=*************"; exit 1; fi
	./$(BINARY_NAME) -i examples/download.json -action download -host $(HOST)

# 使用自定义协议和端口的示例
.PHONY: example-http
example-http: build
	@if [ -z "$(HOST)" ]; then echo "请设置 HOST 变量，例如: make example-http HOST=*************"; exit 1; fi
	./$(BINARY_NAME) -i examples/status.json -action status -host $(HOST) -protocol http -port 8080

# 帮助
.PHONY: help
help:
	@echo "可用的命令:"
	@echo "  make build          - 编译程序"
	@echo "  make build-dir      - 编译到 build 目录"
	@echo "  make build-linux    - 交叉编译 Linux 版本"
	@echo "  make build-windows  - 交叉编译 Windows 版本"
	@echo "  make deps           - 下载依赖"
	@echo "  make clean          - 清理编译文件"
	@echo "  make fmt            - 格式化代码"
	@echo "  make vet            - 检查代码"
	@echo "  make help           - 显示帮助"
	@echo ""
	@echo "运行示例 (需要设置 HOST 变量):"
	@echo "  make example-register HOST=*************"
	@echo "  make example-status HOST=*************"
	@echo "  make example-upload HOST=*************"
	@echo "  make example-download HOST=*************"
	@echo "  make example-http HOST=*************"
	@echo ""
	@echo "直接使用命令行:"
	@echo "  ./jl_client_bin -i examples/register.json -action register -host *************"
	@echo "  ./jl_client_bin -i examples/status.json -action status -host ************* -port 8080 -protocol http"
