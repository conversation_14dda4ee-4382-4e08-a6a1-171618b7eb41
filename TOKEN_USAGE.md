# Token 命令使用说明

## 概述

新增的 `token` 命令允许你单独获取访问令牌，而无需执行其他业务操作。这对于调试、测试或预先获取令牌非常有用。

## 基本用法

```bash
./jl_client_bin.exe -action token -host <服务器地址>
```

## 命令参数

| 参数 | 说明 | 是否必需 | 默认值 |
|------|------|----------|--------|
| `-action` | 必须设置为 `token` | 是 | - |
| `-host` | 服务器 IP 地址或主机名 | 是 | - |
| `-port` | 服务器端口 | 否 | `8888` |
| `-protocol` | 协议类型 | 否 | `https` |

**重要**: `token` 命令不需要也不接受 `-i` 参数（输入文件）

## 使用示例

### 基本使用
```bash
# 使用默认端口和协议
./jl_client_bin.exe -action token -host *************
```

### 自定义端口
```bash
# 使用自定义端口
./jl_client_bin.exe -action token -host ************* -port 9999
```

### 使用 HTTP 协议
```bash
# 使用 HTTP 协议和自定义端口
./jl_client_bin.exe -action token -host ************* -port 8080 -protocol http
```

### 使用域名
```bash
# 使用域名
./jl_client_bin.exe -action token -host api.example.com
```

## 输出示例

成功获取 token 时的输出：
```
Token obtained successfully: cW9QcC9FSFprSmxTOEM5R3JwZVN4M09Na2ZTNzUyZWhvYUdCVDdvUWVaaz0=
Token saved to .token file
```

## 工作流程

1. **读取配置**: 从 `.env` 文件读取 `CLIENT_ID` 和 `CLIENT_SECRET`
2. **检查现有 token**: 如果 `.token` 文件存在且有效，直接返回
3. **获取随机数**: 如果需要，调用 `/query` 接口获取随机数
4. **计算密钥**: 使用 SM3 算法计算认证密钥
5. **获取 token**: 调用 `/token` 接口获取访问令牌
6. **保存 token**: 将令牌保存到 `.token` 文件
7. **显示结果**: 在控制台显示获取的令牌

## 文件操作

### 生成的文件
- `.randomNum`: 存储从服务器获取的随机数
- `.token`: 存储获取的访问令牌

### 配置文件
确保 `.env` 文件包含正确的配置：
```
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
```

## 错误处理

常见错误及解决方法：

### 1. 缺少必需参数
```
Host (-host) is required
```
**解决**: 提供 `-host` 参数

### 2. 配置文件错误
```
CLIENT_ID and CLIENT_SECRET must be set in .env file
```
**解决**: 检查 `.env` 文件是否存在且包含正确的配置

### 3. 网络连接错误
```
Failed to get token: Post "https://...": dial tcp: connect: connection refused
```
**解决**: 检查网络连接和服务器地址

### 4. 认证失败
```
Failed to get token: failed to get token: 安全凭证验证失败
```
**解决**: 检查 `CLIENT_ID` 和 `CLIENT_SECRET` 是否正确

## 与其他命令的区别

| 特性 | token 命令 | 其他业务命令 |
|------|------------|--------------|
| 需要输入文件 | ❌ | ✅ |
| 获取 token | ✅ | ✅ (自动) |
| 执行业务操作 | ❌ | ✅ |
| 显示 token | ✅ | ❌ |

## 使用场景

1. **调试**: 验证认证流程是否正常
2. **测试**: 检查服务器连接和凭据
3. **预获取**: 在执行业务操作前预先获取令牌
4. **监控**: 定期检查令牌获取是否正常

## 注意事项

1. 令牌有过期时间，程序会自动处理过期令牌的重新获取
2. 每次运行 `token` 命令都会尝试获取新的令牌
3. 令牌会覆盖保存到 `.token` 文件中
4. 程序会忽略 SSL 证书验证（适用于测试环境）
