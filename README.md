# JL Client

这是一个用于与 JG 接口进行交互的 Go 客户端工具。

## 功能特性

- 自动处理随机数获取和 Token 认证
- 支持系统注册、更新、状态上报、数据上传和下载
- 使用 SM3 加密算法进行安全认证
- 支持 HTTPS 连接（忽略 SSL 证书验证）
- 配置文件管理

## 安装和编译

1. 确保已安装 Go 1.21 或更高版本
2. 下载依赖：
```bash
go mod tidy
```

3. 编译程序：
```bash
go build -o jl_client_bin main.go
```

## 配置

1. 编辑 `.env` 文件，设置你的客户端 ID 和密钥：
```
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
```

2. 确保接口地址正确（在 main.go 中的 BaseURL 常量）

## 使用方法

基本命令格式：
```bash
./jl_client_bin -i input.json -action <action_name>
```

### 支持的操作

1. **系统注册**
```bash
./jl_client_bin -i examples/register.json -action register
```

2. **系统信息更新**
```bash
./jl_client_bin -i examples/update.json -action update
```

3. **系统状态上报**
```bash
./jl_client_bin -i examples/status.json -action status
```

4. **数据上报**
```bash
./jl_client_bin -i examples/upload.json -action upload
```

5. **数据下载**
```bash
./jl_client_bin -i examples/download.json -action download
```

## 输入文件格式

### 注册 (register.json)
```json
{
    "org": "单位名称",
    "org_type": "单位类别",
    "secret_qualification": "保密资质",
    "net_license_number": "涉密网络运行许可证书编号",
    "net_name": "涉密网络名称",
    "net_type": "网络类型",
    "zjg_license_number": "保密自监管软件系统证书编号",
    "USCI_number": "统一社会信用代码",
    "postal_code": "区域邮政编码",
    "usciNumber": "统一社会信用代码"
}
```

### 更新 (update.json)
```json
{
    "org": "单位名称",
    "org_type": "单位类别",
    "secret_qualification": "保密资质",
    "net_license_number": "涉密网络运行许可证书编号",
    "net_name": "涉密网络名称",
    "zjg_license_number": "保密自监管软件系统证书编号"
}
```

### 状态上报 (status.json)
```json
{
    "ssa_run_state": 1
}
```

### 数据上报 (upload.json)
```json
{
    "type": 1,
    "data": {
        "event_id": "事件ID",
        "event_name": "事件名称",
        "description": "事件描述"
    }
}
```

### 数据下载 (download.json)
```json
{
    "notice_type": 1
}
```

## 文件说明

- `.randomNum`: 存储从服务器获取的随机数
- `.token`: 存储获取的访问令牌
- `.env`: 配置文件，包含客户端 ID 和密钥

## 注意事项

1. 程序会自动处理随机数获取和 Token 认证，无需手动调用
2. Token 会自动缓存，过期时会重新获取
3. 所有接口调用都会自动添加 client_id 参数
4. 状态上报和数据上报会自动添加当前时间（如果未提供）
5. 程序忽略 SSL 证书验证，适用于测试环境

## 错误处理

程序会输出详细的错误信息，包括：
- 网络连接错误
- 认证失败
- 接口调用失败
- 文件读取错误

## 数据类型说明

根据接口文档，数据上报的 type 字段支持以下值：
- 1: 上报事件信息
- 2: 上报处置信息
- 4: 上报协查数据
- 5: 上报协办数据
- 6: 上报风险预警数据
- 7: 上报情报订阅请求
- 8: 上报情报查询请求
- 9: 上报情报订阅应答请求
- 10: 案件线索信息
- 1000: 测试kafka
- 2000: 测试发送service

数据下载的 notice_type 字段支持：
- 1: 督办任务
- 2: 协办任务
- 3: 协查结果
- 4: 风险预警
