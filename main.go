package main

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"github.com/tjfoc/gmsm/sm3"
)

const (
	DefaultProtocol = "https"
	DefaultPort     = "8888"
	BasePath        = "/asscis/api/routing"
)

type Client struct {
	BaseURL      string
	ClientID     string
	ClientSecret string
	HTTPClient   *http.Client
}

type RandomResponse struct {
	Msg    string `json:"msg"`
	Random string `json:"random"`
	Code   int    `json:"code"`
}

type TokenResponse struct {
	Msg         string `json:"msg"`
	AccessToken string `json:"access_token"`
	Code        int    `json:"code"`
	ExpiresIn   int    `json:"expires_in"`
}

type APIResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func NewClient(protocol, host, port string) *Client {
	// 加载 .env 文件
	err := godotenv.Load()
	if err != nil {
		log.Printf("Warning: Error loading .env file: %v", err)
	}

	clientID := os.Getenv("CLIENT_ID")
	clientSecret := os.Getenv("CLIENT_SECRET")

	if clientID == "" || clientSecret == "" {
		log.Fatal("CLIENT_ID and CLIENT_SECRET must be set in .env file")
	}

	// 构建完整的 BaseURL
	baseURL := fmt.Sprintf("%s://%s:%s%s", protocol, host, port, BasePath)

	// 创建忽略SSL证书验证的HTTP客户端
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	return &Client{
		BaseURL:      baseURL,
		ClientID:     clientID,
		ClientSecret: clientSecret,
		HTTPClient:   &http.Client{Transport: tr, Timeout: 30 * time.Second},
	}
}

// SM3 哈希函数
func sm3Hash(data string) string {
	h := sm3.New()
	h.Write([]byte(data))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// 获取随机数
func (c *Client) GetRandom() (string, error) {
	reqData := map[string]string{
		"client_id": c.ClientID,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", err
	}

	resp, err := c.HTTPClient.Post(c.BaseURL+"/query", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var randomResp RandomResponse
	err = json.Unmarshal(body, &randomResp)
	if err != nil {
		return "", err
	}

	if randomResp.Code != 200 {
		return "", fmt.Errorf("failed to get random: %s", randomResp.Msg)
	}

	// 保存随机数到文件
	err = os.WriteFile(".randomNum", []byte(randomResp.Random), 0644)
	if err != nil {
		return "", err
	}

	return randomResp.Random, nil
}

// 获取Token
func (c *Client) GetToken() (string, error) {
	// 读取随机数
	randomBytes, err := os.ReadFile(".randomNum")
	if err != nil {
		return "", fmt.Errorf("failed to read random number: %v", err)
	}
	random := strings.TrimSpace(string(randomBytes))

	// 计算secretSM3
	// 第一步：使用SM3对clientSecret加密得到值a
	a := sm3Hash(c.ClientSecret)
	// 第二步：使用SM3对(随机数+a)加密得到secretSM3
	secretSM3 := sm3Hash(random + a)

	reqData := map[string]string{
		"client_id": c.ClientID,
		"secretSM3": secretSM3,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", err
	}

	resp, err := c.HTTPClient.Post(c.BaseURL+"/token", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var tokenResp TokenResponse
	err = json.Unmarshal(body, &tokenResp)
	if err != nil {
		return "", err
	}

	if tokenResp.Code != 200 {
		return "", fmt.Errorf("failed to get token: %s", tokenResp.Msg)
	}

	// 保存token到文件
	err = os.WriteFile(".token", []byte(tokenResp.AccessToken), 0644)
	if err != nil {
		return "", err
	}

	return tokenResp.AccessToken, nil
}

// 获取有效的Token
func (c *Client) getValidToken() (string, error) {
	// 尝试读取现有token
	tokenBytes, err := os.ReadFile(".token")
	if err == nil {
		token := strings.TrimSpace(string(tokenBytes))
		if token != "" {
			return token, nil
		}
	}

	// 如果没有token或token无效，重新获取
	_, err = c.GetRandom()
	if err != nil {
		return "", err
	}

	return c.GetToken()
}

// 发送带认证的请求
func (c *Client) sendAuthenticatedRequest(method, endpoint string, data interface{}) (*APIResponse, error) {
	token, err := c.getValidToken()
	if err != nil {
		return nil, err
	}

	var reqBody io.Reader
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, c.BaseURL+endpoint, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var apiResp APIResponse
	err = json.Unmarshal(body, &apiResp)
	if err != nil {
		return nil, err
	}

	return &apiResp, nil
}

// 系统注册
func (c *Client) Register(inputFile string) error {
	data, err := c.loadInputData(inputFile)
	if err != nil {
		return err
	}

	// 添加client_id到数据中
	data["client_id"] = c.ClientID

	resp, err := c.sendAuthenticatedRequest("POST", "/register", data)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		return fmt.Errorf("registration failed: %s", resp.Message)
	}

	fmt.Printf("Registration successful: %s\n", resp.Message)
	return nil
}

// 系统信息更新
func (c *Client) Update(inputFile string) error {
	data, err := c.loadInputData(inputFile)
	if err != nil {
		return err
	}

	// 添加client_id到数据中
	data["client_id"] = c.ClientID

	resp, err := c.sendAuthenticatedRequest("POST", "/update", data)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		return fmt.Errorf("update failed: %s", resp.Message)
	}

	fmt.Printf("Update successful: %s\n", resp.Message)
	return nil
}

// 系统状态上报
func (c *Client) ReportStatus(inputFile string) error {
	data, err := c.loadInputData(inputFile)
	if err != nil {
		return err
	}

	// 添加client_id和当前时间
	data["client_id"] = c.ClientID
	if _, exists := data["update_time"]; !exists {
		data["update_time"] = time.Now().Format("2006-01-02 15:04:05")
	}

	resp, err := c.sendAuthenticatedRequest("POST", "/status", data)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		return fmt.Errorf("status report failed: %s", resp.Message)
	}

	fmt.Printf("Status report successful: %s\n", resp.Message)
	return nil
}

// 数据上报
func (c *Client) UploadData(inputFile string) error {
	data, err := c.loadInputData(inputFile)
	if err != nil {
		return err
	}

	// 添加client_id和当前时间
	data["client_id"] = c.ClientID
	if _, exists := data["update_time"]; !exists {
		data["update_time"] = time.Now().Format("2006-01-02 15:04:05")
	}

	resp, err := c.sendAuthenticatedRequest("POST", "/data", data)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		return fmt.Errorf("data upload failed: %s", resp.Message)
	}

	fmt.Printf("Data upload successful: %s\n", resp.Message)
	return nil
}

// 数据下载
func (c *Client) DownloadData(inputFile string) error {
	data, err := c.loadInputData(inputFile)
	if err != nil {
		return err
	}

	// 添加client_id
	data["client_id"] = c.ClientID

	resp, err := c.sendAuthenticatedRequest("GET", "/announce", data)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		return fmt.Errorf("data download failed: %s", resp.Message)
	}

	fmt.Printf("Data download successful: %s\n", resp.Message)
	return nil
}

// 加载输入数据
func (c *Client) loadInputData(inputFile string) (map[string]interface{}, error) {
	if inputFile == "" {
		return nil, fmt.Errorf("input file is required")
	}

	data, err := os.ReadFile(inputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read input file: %v", err)
	}

	var result map[string]interface{}
	err = json.Unmarshal(data, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to parse input JSON: %v", err)
	}

	return result, nil
}

func main() {
	var inputFile string
	var action string
	var host string
	var port string
	var protocol string

	flag.StringVar(&inputFile, "i", "", "Input JSON file path")
	flag.StringVar(&action, "action", "", "Action to perform: register, update, status, upload, download, token")
	flag.StringVar(&host, "host", "", "Interface host/IP address (required)")
	flag.StringVar(&port, "port", DefaultPort, "Interface port (default: 8888)")
	flag.StringVar(&protocol, "protocol", DefaultProtocol, "Interface protocol: http or https (default: https)")
	flag.Parse()

	if action == "" {
		log.Fatal("Action (-action) is required: register, update, status, upload, download, token")
	}

	// token 命令不需要输入文件
	if action != "token" && inputFile == "" {
		log.Fatal("Input file (-i) is required")
	}

	if host == "" {
		log.Fatal("Host (-host) is required")
	}

	// 验证协议参数
	if protocol != "http" && protocol != "https" {
		log.Fatal("Protocol must be 'http' or 'https'")
	}

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		log.Fatalf("Input file does not exist: %s", inputFile)
	}

	client := NewClient(protocol, host, port)

	switch action {
	case "token":
		// 获取并显示 token
		token, err := client.getValidToken()
		if err != nil {
			log.Fatalf("Failed to get token: %v", err)
		}
		fmt.Printf("Token obtained successfully: %s\n", token)
		fmt.Printf("Token saved to .token file\n")
	case "register":
		err := client.Register(inputFile)
		if err != nil {
			log.Fatalf("Registration failed: %v", err)
		}
	case "update":
		err := client.Update(inputFile)
		if err != nil {
			log.Fatalf("Update failed: %v", err)
		}
	case "status":
		err := client.ReportStatus(inputFile)
		if err != nil {
			log.Fatalf("Status report failed: %v", err)
		}
	case "upload":
		err := client.UploadData(inputFile)
		if err != nil {
			log.Fatalf("Data upload failed: %v", err)
		}
	case "download":
		err := client.DownloadData(inputFile)
		if err != nil {
			log.Fatalf("Data download failed: %v", err)
		}
	default:
		log.Fatalf("Unknown action: %s. Available actions: register, update, status, upload, download, token", action)
	}
}
